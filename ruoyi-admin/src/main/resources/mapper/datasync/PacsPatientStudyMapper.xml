<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.datasync.mapper.PacsPatientStudyMapper">

    <resultMap type="PacsPatientStudy" id="PacsPatientStudyResult">
        <result property="id"    column="id"    />
        <result property="hospitalId"    column="hospital_id"    />
        <result property="hospitalName"    column="hospital_name"    />
        <result property="originalPatientId"    column="original_patient_id"    />
        <result property="patientName"    column="patient_name"    />
        <result property="originalExamCode"    column="original_exam_code"    />
        <result property="examCode"    column="exam_code"    />
        <result property="modality"    column="modality"    />
        <result property="mobile"    column="mobile"    />
        <result property="patientSex"    column="patient_sex"    />
        <result property="hisPatientId"    column="his_patient_id"    />
        <result property="outPatientId"    column="out_patient_id"    />
        <result property="inPatientId"    column="in_patient_id"    />
        <result property="examPatientId"    column="exam_patient_id"    />
        <result property="patientFrom"    column="patient_from"    />
        <result property="bedNo"    column="bed_no"    />
        <result property="patientBirthday"    column="patient_birthday" jdbcType="DATE"    />
        <result property="organ"    column="organ"    />
        <result property="examItem"    column="exam_item"    />
        <result property="examDepartment"    column="exam_department"    />
        <result property="examDoctorName"    column="exam_doctor_name"    />
        <result property="registerTime"    column="register_time" jdbcType="TIMESTAMP"    />
        <result property="reserveTime"    column="reserve_time" jdbcType="TIMESTAMP"    />
        <result property="reserveArrivalTime"    column="reserve_arrival_time" jdbcType="TIMESTAMP"    />
        <result property="checkFinishTime"    column="check_finish_time" jdbcType="TIMESTAMP"    />
        <result property="idNo"    column="id_no"    />
        <result property="socialSecurityCardNo"    column="social_security_card_no"    />
        <result property="medicalCardNo"    column="medical_card_no"    />
        <result property="medicalHistory"    column="medical_history"    />
        <result property="clinicalSymptom"    column="clinical_symptom"    />
        <result property="clinicalDiagnosis"    column="clinical_diagnosis"    />
        <result property="device"    column="device"    />
        <result property="syncTime"    column="sync_time" jdbcType="TIMESTAMP"    />
        <result property="updateTime"    column="update_time" jdbcType="TIMESTAMP"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="dicomSyncFlag"    column="dicom_sync_flag"    />
        <result property="syncErrorCode"    column="sync_error_code"    />
        <result property="syncErrorMessage"    column="sync_error_message"    />
        <result property="lastSyncAttemptTime"    column="last_sync_attempt_time" jdbcType="TIMESTAMP"    />
        <result property="syncRetryCount"    column="sync_retry_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time" jdbcType="TIMESTAMP"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="see"    column="see"    />
        <result property="reportDiagnose"    column="report_diagnose"    />
        <result property="diagnosisStatus"    column="diagnosis_status"    />
    </resultMap>

    <sql id="selectPacsPatientStudyVo">
        select diagnosis_status,see,id, hospital_id, hospital_name, original_patient_id, patient_name, original_exam_code, exam_code, modality, mobile, patient_sex, his_patient_id, out_patient_id, in_patient_id, exam_patient_id, patient_from, bed_no, patient_birthday, organ, exam_item, exam_department, exam_doctor_name, register_time, reserve_time, reserve_arrival_time, check_finish_time, id_no, social_security_card_no, medical_card_no, medical_history, clinical_symptom, clinical_diagnosis, device, sync_time, update_time, is_deleted, dicom_sync_flag, sync_error_code, sync_error_message, last_sync_attempt_time, sync_retry_count, create_by, create_time, update_by, remark,report_diagnose from pacs_patient_study
    </sql>

    <select id="selectPacsPatientStudyList" parameterType="PacsPatientStudy" resultMap="PacsPatientStudyResult">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            modality in ('MG','MR','DX','CT','CR')
            <if test="hospitalId != null  and hospitalId != ''"> and hospital_id = #{hospitalId}</if>
            <if test="hospitalName != null  and hospitalName != ''"> and hospital_name like concat('%', #{hospitalName}, '%')</if>
            <if test="originalPatientId != null  and originalPatientId != ''"> and original_patient_id = #{originalPatientId}</if>
            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="originalExamCode != null  and originalExamCode != ''"> and original_exam_code = #{originalExamCode}</if>
            <if test="examCode != null  and examCode != ''"> and exam_code = #{examCode}</if>
            <if test="modality != null  and modality != ''"> and modality = #{modality}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="patientSex != null  and patientSex != ''"> and patient_sex = #{patientSex}</if>
            <if test="hisPatientId != null  and hisPatientId != ''"> and his_patient_id = #{hisPatientId}</if>
            <if test="outPatientId != null  and outPatientId != ''"> and out_patient_id = #{outPatientId}</if>
            <if test="inPatientId != null  and inPatientId != ''"> and in_patient_id = #{inPatientId}</if>
            <if test="examPatientId != null  and examPatientId != ''"> and exam_patient_id = #{examPatientId}</if>
            <if test="patientFrom != null  and patientFrom != ''"> and patient_from = #{patientFrom}</if>
            <if test="bedNo != null  and bedNo != ''"> and bed_no = #{bedNo}</if>
            <if test="patientBirthday != null "> and patient_birthday = #{patientBirthday}</if>
            <if test="organ != null  and organ != ''"> and organ = #{organ}</if>
            <if test="examItem != null  and examItem != ''"> and exam_item = #{examItem}</if>
            <if test="examDepartment != null  and examDepartment != ''"> and exam_department = #{examDepartment}</if>
            <if test="examDoctorName != null  and examDoctorName != ''"> and exam_doctor_name like concat('%', #{examDoctorName}, '%')</if>
            <if test="registerTime != null "> and DATE(register_time) = DATE(#{registerTime})</if>
            <if test="reserveTime != null "> and DATE(reserve_time) = DATE(#{reserveTime})</if>
            <if test="reserveArrivalTime != null "> and DATE(reserve_arrival_time) = DATE(#{reserveArrivalTime})</if>
            <if test="checkFinishTime != null "> and DATE(check_finish_time) = DATE(#{checkFinishTime})</if>
            <if test="idNo != null  and idNo != ''"> and id_no = #{idNo}</if>
            <if test="socialSecurityCardNo != null  and socialSecurityCardNo != ''"> and social_security_card_no = #{socialSecurityCardNo}</if>
            <if test="medicalCardNo != null  and medicalCardNo != ''"> and medical_card_no = #{medicalCardNo}</if>
            <if test="medicalHistory != null  and medicalHistory != ''"> and medical_history = #{medicalHistory}</if>
            <if test="clinicalSymptom != null  and clinicalSymptom != ''"> and clinical_symptom = #{clinicalSymptom}</if>
            <if test="clinicalDiagnosis != null  and clinicalDiagnosis != ''"> and clinical_diagnosis = #{clinicalDiagnosis}</if>
            <if test="device != null  and device != ''"> and device = #{device}</if>
            <if test="syncTime != null "> and DATE(sync_time) = DATE(#{syncTime})</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="dicomSyncFlag != null  and dicomSyncFlag != ''"> and dicom_sync_flag = #{dicomSyncFlag}</if>
            <if test="diagnosisStatus != null  and diagnosisStatus != ''"> and diagnosis_status = #{diagnosisStatus}</if>
        </where>
        order by check_finish_time desc
    </select>

    <select id="selectPacsPatientStudyById" parameterType="Long" resultMap="PacsPatientStudyResult">
        <include refid="selectPacsPatientStudyVo"/>
        where id = #{id}
    </select>
    <select id="selectPacsPatientStudyByKey" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            hospital_id = #{hospitalId} and exam_code = #{examCode}
        </where>
    </select>
    <select id="selectUnsyncPacsPatientStudy" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            modality in ('MG','MR','DX','CT','CR') and (dicom_sync_flag IS NULL OR dicom_sync_flag != '1')
        </where>
    </select>

    <insert id="insertPacsPatientStudy" parameterType="PacsPatientStudy" useGeneratedKeys="true" keyProperty="id">
        insert into pacs_patient_study
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalId != null and hospitalId != ''">hospital_id,</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name,</if>
            <if test="originalPatientId != null and originalPatientId != ''">original_patient_id,</if>
            <if test="patientName != null">patient_name,</if>
            <if test="originalExamCode != null and originalExamCode != ''">original_exam_code,</if>
            <if test="examCode != null and examCode != ''">exam_code,</if>
            <if test="modality != null">modality,</if>
            <if test="mobile != null">mobile,</if>
            <if test="patientSex != null">patient_sex,</if>
            <if test="hisPatientId != null">his_patient_id,</if>
            <if test="outPatientId != null">out_patient_id,</if>
            <if test="inPatientId != null">in_patient_id,</if>
            <if test="examPatientId != null">exam_patient_id,</if>
            <if test="patientFrom != null">patient_from,</if>
            <if test="bedNo != null">bed_no,</if>
            <if test="patientBirthday != null">patient_birthday,</if>
            <if test="organ != null">organ,</if>
            <if test="examItem != null">exam_item,</if>
            <if test="examDepartment != null">exam_department,</if>
            <if test="examDoctorName != null">exam_doctor_name,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="reserveTime != null">reserve_time,</if>
            <if test="reserveArrivalTime != null">reserve_arrival_time,</if>
            <if test="checkFinishTime != null">check_finish_time,</if>
            <if test="idNo != null">id_no,</if>
            <if test="socialSecurityCardNo != null">social_security_card_no,</if>
            <if test="medicalCardNo != null">medical_card_no,</if>
            <if test="medicalHistory != null">medical_history,</if>
            <if test="clinicalSymptom != null">clinical_symptom,</if>
            <if test="clinicalDiagnosis != null">clinical_diagnosis,</if>
            <if test="device != null">device,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="dicomSyncFlag != null">dicom_sync_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="reportDiagnose != null">report_diagnose,</if>
            <if test="diagnosisStatus != null"> diagnosis_status,</if>
            <if test="see != null">see,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hospitalId != null and hospitalId != ''">#{hospitalId},</if>
            <if test="hospitalName != null and hospitalName != ''">#{hospitalName},</if>
            <if test="originalPatientId != null and originalPatientId != ''">#{originalPatientId},</if>
            <if test="patientName != null">#{patientName},</if>
            <if test="originalExamCode != null and originalExamCode != ''">#{originalExamCode},</if>
            <if test="examCode != null and examCode != ''">#{examCode},</if>
            <if test="modality != null">#{modality},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="patientSex != null">#{patientSex},</if>
            <if test="hisPatientId != null">#{hisPatientId},</if>
            <if test="outPatientId != null">#{outPatientId},</if>
            <if test="inPatientId != null">#{inPatientId},</if>
            <if test="examPatientId != null">#{examPatientId},</if>
            <if test="patientFrom != null">#{patientFrom},</if>
            <if test="bedNo != null">#{bedNo},</if>
            <if test="patientBirthday != null">#{patientBirthday},</if>
            <if test="organ != null">#{organ},</if>
            <if test="examItem != null">#{examItem},</if>
            <if test="examDepartment != null">#{examDepartment},</if>
            <if test="examDoctorName != null">#{examDoctorName},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="reserveTime != null">#{reserveTime},</if>
            <if test="reserveArrivalTime != null">#{reserveArrivalTime},</if>
            <if test="checkFinishTime != null">#{checkFinishTime},</if>
            <if test="idNo != null">#{idNo},</if>
            <if test="socialSecurityCardNo != null">#{socialSecurityCardNo},</if>
            <if test="medicalCardNo != null">#{medicalCardNo},</if>
            <if test="medicalHistory != null">#{medicalHistory},</if>
            <if test="clinicalSymptom != null">#{clinicalSymptom},</if>
            <if test="clinicalDiagnosis != null">#{clinicalDiagnosis},</if>
            <if test="device != null">#{device},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="dicomSyncFlag != null">#{dicomSyncFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reportDiagnose!= null">#{reportDiagnose},</if>
            <if test="diagnosisStatus != null"> #{diagnosisStatus},</if>
            <if test="see != null"> #{see},</if>
         </trim>
    </insert>

    <update id="updatePacsPatientStudy" parameterType="PacsPatientStudy">
        update pacs_patient_study
        <trim prefix="SET" suffixOverrides=",">
            <if test="hospitalId != null and hospitalId != ''">hospital_id = #{hospitalId},</if>
            <if test="hospitalName != null and hospitalName != ''">hospital_name = #{hospitalName},</if>
            <if test="originalPatientId != null and originalPatientId != ''">original_patient_id = #{originalPatientId},</if>
            <if test="patientName != null">patient_name = #{patientName},</if>
            <if test="originalExamCode != null and originalExamCode != ''">original_exam_code = #{originalExamCode},</if>
            <if test="examCode != null and examCode != ''">exam_code = #{examCode},</if>
            <if test="modality != null">modality = #{modality},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="patientSex != null">patient_sex = #{patientSex},</if>
            <if test="hisPatientId != null">his_patient_id = #{hisPatientId},</if>
            <if test="outPatientId != null">out_patient_id = #{outPatientId},</if>
            <if test="inPatientId != null">in_patient_id = #{inPatientId},</if>
            <if test="examPatientId != null">exam_patient_id = #{examPatientId},</if>
            <if test="patientFrom != null">patient_from = #{patientFrom},</if>
            <if test="bedNo != null">bed_no = #{bedNo},</if>
            <if test="patientBirthday != null">patient_birthday = #{patientBirthday},</if>
            <if test="organ != null">organ = #{organ},</if>
            <if test="examItem != null">exam_item = #{examItem},</if>
            <if test="examDepartment != null">exam_department = #{examDepartment},</if>
            <if test="examDoctorName != null">exam_doctor_name = #{examDoctorName},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="reserveTime != null">reserve_time = #{reserveTime},</if>
            <if test="reserveArrivalTime != null">reserve_arrival_time = #{reserveArrivalTime},</if>
            <if test="checkFinishTime != null">check_finish_time = #{checkFinishTime},</if>
            <if test="idNo != null">id_no = #{idNo},</if>
            <if test="socialSecurityCardNo != null">social_security_card_no = #{socialSecurityCardNo},</if>
            <if test="medicalCardNo != null">medical_card_no = #{medicalCardNo},</if>
            <if test="medicalHistory != null">medical_history = #{medicalHistory},</if>
            <if test="clinicalSymptom != null">clinical_symptom = #{clinicalSymptom},</if>
            <if test="clinicalDiagnosis != null">clinical_diagnosis = #{clinicalDiagnosis},</if>
            <if test="device != null">device = #{device},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="dicomSyncFlag != null">dicom_sync_flag = #{dicomSyncFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reportDiagnose != null">report_diagnose = #{reportDiagnose},</if>
            <if test="diagnosisStatus != null">diagnosis_status = #{diagnosisStatus},</if>
            <if test="see != null">see = #{see},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePacsPatientStudyStatus">
        update pacs_patient_study set dicom_sync_flag = #{status}
        where original_patient_id = #{originalPatientId}
    </update>
    <update id="updateDiagnosisStatus">
        update pacs_patient_study set diagnosis_status = #{diagnosisStatus} where original_patient_id = #{patientId}
    </update>
    <update id="updateDiagnosisStatusById">
        update pacs_patient_study set diagnosis_status = #{diagnosisStatus} where id = #{id}
    </update>

    <update id="updatePacsPatientStudySyncResult">
        update pacs_patient_study
        set dicom_sync_flag = #{status},
            sync_error_code = #{errorCode},
            sync_error_message = #{errorMessage},
            last_sync_attempt_time = NOW(),
            sync_retry_count = #{retryCount}
        where original_patient_id = #{originalPatientId}
    </update>

    <delete id="deletePacsPatientStudyById" parameterType="Long">
        delete from pacs_patient_study where id = #{id}
    </delete>

    <delete id="deletePacsPatientStudyByIds" parameterType="String">
        delete from pacs_patient_study where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- Add implementation for selecting distinct unsynced patient IDs -->
    <select id="selectUnsyncDistinctPacsPatientIds" resultMap="PacsPatientStudyResult">
        SELECT DISTINCT original_patient_id
        FROM pacs_patient_study
        WHERE dicom_sync_flag IS NULL OR dicom_sync_flag != '1'
    </select>

    <select id="selectRetryablePacsPatientStudyList" resultMap="PacsPatientStudyResult">
        <include refid="selectPacsPatientStudyVo"/>
        where dicom_sync_flag = '0'
        and (sync_retry_count IS NULL OR sync_retry_count &lt; #{param1})
        and (last_sync_attempt_time IS NULL OR
             TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) &gt;= #{param2})
        and sync_error_code IN ('CONNECTIVITY_ERROR', 'TIMEOUT', 'PARTIAL_FAILURE', 'SYSTEM_ERROR', 'INTERRUPTED', 'PERMISSION_DENIED')
        order by last_sync_attempt_time ASC, check_finish_time ASC
        limit 50
    </select>

    <select id="getExamList" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            modality in ('MG','MR','DX','CT','CR')
            <if test="idNo != null  and idNo != ''"> and id_no = #{idNo}</if>
            <if test="examCode!=null and examCode!=''"> and exam_code=#{examCode}</if>
            <if test="phone!=null and phone!=''"> and mobile=#{phone}</if>
        </where>
        ORDER BY check_finish_time DESC
    </select>

    <!-- 获取用户及其亲友的检查报告列表（支持快捷时间过滤） -->
    <select id="getExamListWithRelations" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            <!-- 基础过滤条件 -->
            modality in ('MG','MR','DX','CT','CR')

            <!-- 查询条件：支持同时查询用户自己和亲友的记录 -->
            <if test="(idNoList != null and idNoList.size() > 0) or (phone != null and phone != '')">
                AND (
                    <!-- 亲友身份证号查询 -->
                    <if test="idNoList != null and idNoList.size() > 0">
                        id_no IN
                        <foreach collection="idNoList" item="idNo" open="(" separator="," close=")">
                            #{idNo}
                        </foreach>
                    </if>
                    <!-- 如果同时有身份证号列表和手机号，使用OR连接 -->
                    <if test="(idNoList != null and idNoList.size() > 0) and (phone != null and phone != '')">
                        OR
                    </if>
                    <!-- 用户自己的手机号查询 -->
                    <if test="phone != null and phone != ''">
                        mobile = #{phone}
                    </if>
                )
            </if>

            <!-- 检查号查询（精确匹配） -->
            <if test="examCode != null and examCode != ''">
                AND exam_code = #{examCode}
            </if>

            <!-- 快捷时间过滤 -->
            <if test="quickFilter != null and quickFilter != '' and quickFilter != 'all'">
                <choose>
                    <when test="quickFilter == 'month1'">
                        AND check_finish_time >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
                    </when>
                    <when test="quickFilter == 'month3'">
                        AND check_finish_time >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
                    </when>
                    <when test="quickFilter == 'year1'">
                        AND check_finish_time >= DATE_SUB(NOW(), INTERVAL 1 YEAR)
                    </when>
                    <when test="quickFilter == 'year2'">
                        AND check_finish_time >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
                    </when>
                    <when test="quickFilter == 'year3'">
                        AND check_finish_time >= DATE_SUB(NOW(), INTERVAL 3 YEAR)
                    </when>
                </choose>
            </if>

            <!-- 确保有查询条件，避免全表查询 -->
            <if test="(idNoList == null or idNoList.size() == 0) and (examCode == null or examCode == '') and (phone == null or phone == '')">
                AND 1 = 0  <!-- 没有任何查询条件时返回空结果 -->
            </if>
        </where>
        ORDER BY check_finish_time DESC
    </select>
    <select id="selectPendingByPeriod" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            modality in ('MG','MR','DX','CT','CR') and (dicom_sync_flag IS NULL OR dicom_sync_flag!= '1')
            and check_finish_time &gt;= #{startTime} and check_finish_time &lt;= #{endTime} order by check_finish_time desc
        </where>
    </select>

    <!-- 为诊断功能查询检查列表，联合查询关联的诊断 -->
    <resultMap id="PacsPatientStudyWithDiagnosisResult" type="com.ruoyi.datasync.domain.PacsPatientStudy" extends="PacsPatientStudyResult">
        <association property="diagnosis" javaType="com.ruoyi.diagnosis.domain.Diagnosis" columnPrefix="d_">
            <id property="id" column="id"/>
            <result property="diagnose" column="diagnose"/>
            <result property="doctor" column="doctor"/>
            <result property="studyId" column="study_id"/>
            <result property="patientId" column="patient_id"/>
            <result property="recommendation" column="recommendation"/>
            <result property="status" column="status"/>
            <result property="diagnosisType" column="type"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
        </association>
    </resultMap>

    <select id="getExamListWithDiagnosis" resultMap="PacsPatientStudyWithDiagnosisResult">
        SELECT p.*
        FROM pacs_patient_study p
        LEFT JOIN diagnosis d ON p.id = d.check_id
        <where>
            p.modality in ('MG','MR','DX','CT','CR')
            <if test="patientId != null and patientId != ''">AND p.original_patient_id = #{patientId}</if>
            <if test="patientName != null and patientName != ''">AND p.patient_name like concat('%', #{patientName},
                '%')
            </if>
            <if test="examCode != null and examCode != ''">AND p.exam_code = #{examCode}</if>
            <if test="examItem != null and examItem != ''">AND p.exam_item like concat('%', #{examItem}, '%')</if>
            <if test="diagnosisStatus != null and diagnosisStatus != ''">AND p.diagnosis_status = #{diagnosisStatus}
            </if>
            <if test="hospitalId != null and hospitalId != ''">AND p.hospital_id = #{hospitalId}</if>
            <if test="hospitalName != null and hospitalName != ''">AND p.hospital_name like concat('%', #{hospitalName},
                '%')
            </if>

            <!-- 场景过滤逻辑 -->
            <if test="scenario != null and scenario != ''">
                <!-- 诊断场景 -->
                <if test="scenario == 'diagnoser_pending'">
                    <!-- 未诊断的检查记录 -->
                    AND p.diagnosis_status = '-1'
                </if>
                <if test="scenario == 'diagnoser_diagnosed'">
                    <!-- 已诊断检查记录（自己诊断的） -->
                    AND p.diagnosis_status = '1' AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_my_all'">
                    <!-- 自己诊断的检查记录（所有状态） -->
                    AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_audited_pass'">
                    <!-- 自己诊断的审核通过记录 -->
                    AND p.diagnosis_status = '2' AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_audited_reject'">
                    <!-- 自己诊断但审核没通过记录 -->
                    AND p.diagnosis_status = '9' AND d.create_by = #{currentUser}
                </if>

                <!-- 审核场景 -->
                <if test="scenario == 'auditor_pending'">
                    <!-- 待审核记录（已诊断但不是自己创建的） -->
                    AND p.diagnosis_status = '1'
                </if>
                <if test="scenario == 'auditor_my_audited'">
                    <!-- 自己已审核记录 -->
                    AND p.diagnosis_status = '2' AND d.audit_by = #{currentUser}
                </if>
                <if test="scenario == 'auditor_my_rejected'">
                    <!-- 自己审核没给通过记录 -->
                    AND p.diagnosis_status = '9' AND d.audit_by = #{currentUser}
                </if>
            </if>
    </where>
        ORDER BY p.register_time DESC
    </select>

    <!-- 获取诊断状态统计 - 更新支持场景过滤 -->
    <select id="getDiagnosisStatusCount" resultType="map">
        SELECT
            COALESCE(SUM(CASE WHEN p.diagnosis_status = '-1' THEN 1 ELSE 0 END), 0) as 'pending',
            COALESCE(SUM(CASE WHEN p.diagnosis_status = '1' THEN 1 ELSE 0 END), 0) as 'diagnosed',
            COALESCE(SUM(CASE WHEN p.diagnosis_status = '2' THEN 1 ELSE 0 END), 0) as 'audited',
            COALESCE(SUM(CASE WHEN p.diagnosis_status = '9' THEN 1 ELSE 0 END), 0) as 'rejected'
        FROM pacs_patient_study p
        LEFT JOIN diagnosis d ON p.id = d.check_id
        <where>
            p.modality in ('MG','MR','DX','CT','CR')
            <if test="patientId != null and patientId != ''"> AND p.original_patient_id = #{patientId}</if>
            <if test="patientName != null and patientName != ''"> AND p.patient_name like concat('%', #{patientName}, '%')</if>
            <if test="examCode != null and examCode != ''"> AND p.exam_code = #{examCode}</if>
            <if test="examItem != null and examItem != ''"> AND p.exam_item like concat('%', #{examItem}, '%')</if>
            <if test="hospitalId != null and hospitalId != ''"> AND p.hospital_id = #{hospitalId}</if>
            <if test="hospitalName != null and hospitalName != ''"> AND p.hospital_name like concat('%', #{hospitalName}, '%')</if>

            <!-- 场景过滤逻辑 -->
            <if test="scenario != null and scenario != ''">
                <!-- 诊断场景 -->
                <if test="scenario == 'diagnoser_pending'">
                    AND p.diagnosis_status = '-1'
                </if>
                <if test="scenario == 'diagnoser_diagnosed'">
                    AND p.diagnosis_status = '1' AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_my_all'">
                    AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_audited_pass'">
                    AND p.diagnosis_status = '2' AND d.create_by = #{currentUser}
                </if>
                <if test="scenario == 'diagnoser_audited_reject'">
                    AND p.diagnosis_status = '9' AND d.create_by = #{currentUser}
                </if>

                <!-- 审核场景 -->
                <if test="scenario == 'auditor_pending'">
                    AND p.diagnosis_status = '1' AND (d.create_by != #{currentUser} OR d.create_by IS NULL)
                </if>
                <if test="scenario == 'auditor_my_audited'">
                    AND p.diagnosis_status = '2' AND d.audit_by = #{currentUser}
                </if>
                <if test="scenario == 'auditor_my_rejected'">
                    AND p.diagnosis_status = '9' AND d.audit_by = #{currentUser}
                </if>
            </if>
        </where>
    </select>
    <select id="selectPacsPatientStudyByExamCode" resultType="com.ruoyi.datasync.domain.PacsPatientStudy">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            exam_code = #{examCode}
        </where>
    </select>

    <select id="selectPacsPatientStudyByPatientId" resultMap="PacsPatientStudyResult">
        <include refid="selectPacsPatientStudyVo"/>
        <where>
            original_patient_id = #{patientId}
        </where>
        ORDER BY check_finish_time DESC
        LIMIT 1
    </select>

    <!-- 查询待同步的患者ID列表 -->
    <select id="selectPendingSyncPatients" resultType="java.lang.String">
        SELECT DISTINCT original_patient_id
        FROM pacs_patient_study
        WHERE modality in ('MG','MR','DX','CT','CR')
          AND (dicom_sync_flag IS NULL OR dicom_sync_flag = '0')
          AND check_finish_time IS NOT NULL
        ORDER BY check_finish_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询同步失败的患者ID列表 -->
    <select id="selectFailedSyncPatients" resultType="java.lang.String">
        SELECT DISTINCT original_patient_id
        FROM pacs_patient_study
        WHERE modality in ('MG','MR','DX','CT','CR')
          AND dicom_sync_flag = '2'
          AND (sync_retry_count IS NULL OR sync_retry_count &lt; #{maxRetryCount})
          AND (last_sync_attempt_time IS NULL OR
               TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) &gt;= 30)
        ORDER BY last_sync_attempt_time ASC, check_finish_time DESC
        LIMIT #{limit}
    </select>

    <select id="selectPacsPatientStudyByIds" parameterType="Long" resultMap="PacsPatientStudyResult">
        <include refid="selectPacsPatientStudyVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
