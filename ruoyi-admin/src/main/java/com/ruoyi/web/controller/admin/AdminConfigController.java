package com.ruoyi.web.controller.admin;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理端配置Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/config")
public class AdminConfigController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AdminConfigController.class);

    @Autowired
    private ISysConfigService configService;

    /**
     * 获取登录页配置信息
     *
     * @return 登录页配置
     */
    @GetMapping("/login")
    public AjaxResult getLoginConfig()
    {
        try {
            Map<String, String> config = new HashMap<>();

            // 获取各项配置
            config.put("hospitalName", configService.selectConfigByKey("admin.login.hospitalName"));
            config.put("systemName", configService.selectConfigByKey("admin.login.systemName"));
            config.put("footerText", configService.selectConfigByKey("admin.login.footerText"));

            // 设置默认值（如果配置为空）
            if (config.get("hospitalName") == null || config.get("hospitalName").isEmpty()) {
                config.put("hospitalName", "鄂托克旗人民医院");
            }
            if (config.get("systemName") == null || config.get("systemName").isEmpty()) {
                config.put("systemName", "PACS医疗影像系统");
            }
            if (config.get("footerText") == null || config.get("footerText").isEmpty()) {
                config.put("footerText", "Copyright © 2018-2025鄂托克旗人民医院 All Rights Reserved.");
            }

            return AjaxResult.success("获取成功", config);
        }
        catch (Exception e)
        {
            log.error("获取登录页配置失败", e);
            return AjaxResult.error("获取登录页配置失败：" + e.getMessage());
        }
    }
}